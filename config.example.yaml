server:
  http_port: 8080       # HTTP服务端口
  sip_ip: "0.0.0.0"     # SIP监听IP
  sip_port: 5060        # SIP监听端口
  sip_id: "34020000002000000001" # 网关国标ID
  sip_domain: "3402000000"       # 网关域
  catalog_query_interval: 10    # catalog查询间隔(秒)，默认10秒
  device_expire_timeout: 30     # 设备过期超时时间(秒)，默认30秒

log:
  level: "debug"        # 日志级别: debug(启用API文档), info, warn, error
  path: "/var/log/gb-gateway.log"
