# SIP消息处理实现文档

## 概述

本文档描述了GB-Gateway中SIP消息处理功能的实现，参考了[panjjo/gosip](https://github.com/panjjo/gosip/blob/master/sip/handler.go)的处理方式。

## 实现的功能

### 1. 消息处理流程

`handlerMessage`函数实现了完整的SIP MESSAGE处理流程：

1. **解析platform信息** - 从SIP请求的From头部提取平台ID
2. **判断是否存在body数据** - 检查Content-Length头部
3. **判断body的数据类型** - 解析XML确定CmdType
4. **根据类型分别处理** - 分发到具体的处理函数

### 2. 支持的消息类型

#### Catalog（设备列表）
- **功能**: 处理平台返回的设备列表响应
- **实现**: `handleCatalogResponse`
- **处理逻辑**:
  - 解析CatalogResponse XML结构
  - 将设备列表保存到内存状态管理器
  - 发送响应到等待的channel（用于异步API调用）

#### Keepalive（心跳）
- **功能**: 处理平台发送的心跳消息
- **实现**: `handleKeepalive`
- **处理逻辑**:
  - 解析KeepaliveNotify XML结构
  - 更新平台最后心跳时间
  - 心跳后异步同步注册设备列表信息

#### DeviceInfo（设备信息）
- **功能**: 处理平台返回的设备信息响应
- **实现**: `handleDeviceInfo`
- **处理逻辑**:
  - 解析DeviceInfoResponse XML结构
  - 更新平台信息（制造商、型号等）

### 3. XML结构定义

在`pkg/models/xml.go`中定义了所有相关的XML结构体：

```go
// 消息外层结构，用于判断消息类型
type MessageReceive struct {
    CmdType string `xml:"CmdType"`
    SN      int    `xml:"SN"`
}

// 设备列表响应
type CatalogResponse struct {
    XMLName    xml.Name `xml:"Response"`
    CmdType    string   `xml:"CmdType"`
    SN         int      `xml:"SN"`
    DeviceID   string   `xml:"DeviceID"`
    SumNum     int      `xml:"SumNum"`
    DeviceList struct {
        Devices []Device `xml:"Item"`
    } `xml:"DeviceList"`
}

// 心跳通知
type KeepaliveNotify struct {
    XMLName  xml.Name `xml:"Notify"`
    CmdType  string   `xml:"CmdType"`
    SN       int      `xml:"SN"`
    DeviceID string   `xml:"DeviceID"`
    Status   string   `xml:"Status"`
}

// 设备信息响应
type DeviceInfoResponse struct {
    XMLName      xml.Name `xml:"Response"`
    CmdType      string   `xml:"CmdType"`
    SN           int      `xml:"SN"`
    DeviceID     string   `xml:"DeviceID"`
    DeviceName   string   `xml:"DeviceName"`
    Manufacturer string   `xml:"Manufacturer"`
    Model        string   `xml:"Model"`
    Firmware     string   `xml:"Firmware"`
    Channel      int      `xml:"Channel"`
}
```

### 4. XML解析工具

在`pkg/utils/xml.go`中实现了XML解析相关的工具函数：

- `XMLDecode` - XML解码
- `XMLEncode` - XML编码
- `XMLEncodeWithHeader` - 带XML头部的编码
- `GbkToUtf8` - GBK到UTF-8转换（处理编码问题）
- `Utf8ToGbk` - UTF-8到GBK转换

### 5. 错误处理

实现了完善的错误处理机制：

- XML解析失败时尝试GBK到UTF-8转换
- 记录详细的错误日志
- 返回适当的HTTP状态码

### 6. 测试覆盖

实现了完整的测试覆盖：

- `pkg/utils/xml_test.go` - XML解析功能测试
- `internal/sip/message_test.go` - 消息处理集成测试

## 使用示例

### Catalog响应处理
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <CmdType>Catalog</CmdType>
    <SN>123</SN>
    <DeviceID>34020000002000000001</DeviceID>
    <SumNum>2</SumNum>
    <DeviceList Num="2">
        <Item>
            <DeviceID>34020000001320000001</DeviceID>
            <Name>Camera 1</Name>
            <Status>ON</Status>
            <IPAddress>*************</IPAddress>
        </Item>
    </DeviceList>
</Response>
```

### Keepalive通知处理
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Notify>
    <CmdType>Keepalive</CmdType>
    <SN>456</SN>
    <DeviceID>34020000002000000001</DeviceID>
    <Status>OK</Status>
</Notify>
```

### DeviceInfo响应处理
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <CmdType>DeviceInfo</CmdType>
    <SN>789</SN>
    <DeviceID>34020000002000000001</DeviceID>
    <DeviceName>Hikvision Platform</DeviceName>
    <Manufacturer>Hikvision</Manufacturer>
    <Model>DS-7816N-K2</Model>
    <Firmware>V4.30.000</Firmware>
    <Channel>16</Channel>
</Response>
```

## 特性

1. **完全兼容GB28181标准** - 严格按照国标协议实现
2. **编码兼容性** - 支持UTF-8和GBK编码自动转换
3. **异步处理** - 心跳后异步触发设备列表同步
4. **内存状态管理** - 设备列表和平台信息实时更新
5. **完整测试覆盖** - 单元测试和集成测试全覆盖

## 日志输出

系统会输出详细的日志信息：

```
INFO Platform registered id=34020000002000000001 uri=sip:34020000002000000001@*************:5060
DEBUG Catalog response received platform_id=34020000002000000001 device_count=2 sn=123
INFO Devices updated platform_id=34020000002000000001 count=2
DEBUG Keepalive received platform_id=34020000002000000001 device_id=34020000002000000001 status=OK
INFO Platform device info updated platform_id=34020000002000000001 manufacturer=Hikvision model=DS-7816N-K2
```

这样的实现确保了GB-Gateway能够正确处理来自GB28181平台的各种SIP消息，为上层应用提供稳定可靠的设备管理和状态同步功能。
