.PHONY: build run clean test docs swagger-gen help

# Variables
BINARY_NAME=gb-gateway
MAIN_PATH=./cmd/gb-gateway
BUILD_DIR=./build
CONFIG_FILE=config.yaml

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

# Build flags
LDFLAGS=-ldflags "-X main.version=$(shell git describe --tags --always --dirty)"

help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

build: ## Build the binary
	@echo "Building $(BINARY_NAME)..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PATH)
	@echo "Build complete: $(BUILD_DIR)/$(BINARY_NAME)"

run: build ## Build and run the application
	@echo "Starting $(BINARY_NAME)..."
	@if [ ! -f $(CONFIG_FILE) ]; then \
		echo "Config file not found, copying from example..."; \
		cp config.example.yaml $(CONFIG_FILE); \
	fi
	$(BUILD_DIR)/$(BINARY_NAME) -config $(CONFIG_FILE)

clean: ## Clean build artifacts
	@echo "Cleaning..."
	$(GOCLEAN)
	@rm -rf $(BUILD_DIR)
	@echo "Clean complete"

test: ## Run tests
	@echo "Running tests..."
	$(GOTEST) -v ./...

deps: ## Download dependencies
	@echo "Downloading dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

# 安装 Swagger 工具
swagger-install:
	@echo "Installing Swagger tools..."
	go install github.com/swaggo/swag/cmd/swag@latest

# 生成 Swagger 文档
swagger-gen:
	@echo "Generating Swagger documentation..."
	swag init -g internal/http/server.go -o docs
	@echo "Swagger documentation generated in docs/"

# 生成文档并启动服务器
docs: swagger-gen run

install: build ## Install the binary to GOPATH/bin
	@echo "Installing $(BINARY_NAME)..."
	@cp $(BUILD_DIR)/$(BINARY_NAME) $(GOPATH)/bin/
	@echo "Installed to $(GOPATH)/bin/$(BINARY_NAME)"

docker-build: ## Build Docker image
	@echo "Building Docker image..."
	docker build -t gb-gateway:latest .

docker-run: docker-build ## Build and run Docker container
	@echo "Running Docker container..."
	docker run -p 8080:8080 -p 5060:5060/udp gb-gateway:latest

lint: ## Run linter
	@echo "Running linter..."
	@which golangci-lint > /dev/null || (echo "Installing golangci-lint..." && curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v1.54.2)
	golangci-lint run

format: ## Format code
	@echo "Formatting code..."
	$(GOCMD) fmt ./...

check: lint test ## Run linter and tests

release: clean swagger-gen build ## Prepare release build
	@echo "Preparing release..."
	@mkdir -p release
	@cp $(BUILD_DIR)/$(BINARY_NAME) release/
	@cp config.example.yaml release/
	@cp README.md release/
	@tar -czf release/gb-gateway-$(shell git describe --tags --always).tar.gz -C release .
	@echo "Release package created: release/gb-gateway-$(shell git describe --tags --always).tar.gz"

dev: ## Start development server with hot reload
	@echo "Starting development server..."
	@which air > /dev/null || (echo "Installing air..." && $(GOGET) -u github.com/cosmtrek/air)
	air

.DEFAULT_GOAL := help
