package sip

import (
	"testing"
	"time"

	"gb-gateway/internal/config"
	"gb-gateway/internal/state"
	"gb-gateway/pkg/models"
)

func TestMessageHandling(t *testing.T) {
	// Create test server
	cfg := &config.ServerConfig{
		SIPIP:     "127.0.0.1",
		SIPPort:   5060,
		SIPID:     "34020000002000000001",
		SIPDomain: "3402000000",
	}
	stateManager := state.NewManager()
	server := NewServer(cfg, stateManager)

	// Register a test platform
	platform := &models.Platform{
		ID:       "34020000002000000001",
		SIPURI:   "sip:34020000002000000001@*************:5060",
		Expires:  3600,
		IP:       "*************",
		Port:     5060,
		LastSeen: time.Now(),
	}
	stateManager.RegisterPlatform(platform)

	t.Run("HandleCatalogResponse", func(t *testing.T) {
		catalogXML := `<?xml version="1.0" encoding="UTF-8"?>
<Response>
	<CmdType>Catalog</CmdType>
	<SN>123</SN>
	<DeviceID>34020000002000000001</DeviceID>
	<SumNum>2</SumNum>
	<DeviceList Num="2">
		<Item>
			<DeviceID>34020000001320000001</DeviceID>
			<Name>Camera 1</Name>
			<Status>ON</Status>
			<IPAddress>*************</IPAddress>
		</Item>
		<Item>
			<DeviceID>34020000001320000002</DeviceID>
			<Name>Camera 2</Name>
			<Status>OFF</Status>
			<IPAddress>*************</IPAddress>
		</Item>
	</DeviceList>
</Response>`

		// Test catalog response handling
		server.handleCatalogResponse("34020000002000000001", []byte(catalogXML))

		// Verify devices were stored
		devices := stateManager.GetAllDevices()
		if len(devices) != 2 {
			t.Errorf("Expected 2 devices, got %d", len(devices))
		}

		// Check first device
		device1, exists := stateManager.GetDevice("34020000001320000001")
		if !exists {
			t.Error("Device 34020000001320000001 not found")
		} else {
			if device1.Name != "Camera 1" {
				t.Errorf("Expected device name 'Camera 1', got '%s'", device1.Name)
			}
			if device1.Status != "ON" {
				t.Errorf("Expected device status 'ON', got '%s'", device1.Status)
			}
			if device1.PlatformID != "34020000002000000001" {
				t.Errorf("Expected platform ID '34020000002000000001', got '%s'", device1.PlatformID)
			}
		}
	})

	t.Run("HandleKeepalive", func(t *testing.T) {
		keepaliveXML := `<?xml version="1.0" encoding="UTF-8"?>
<Notify>
	<CmdType>Keepalive</CmdType>
	<SN>456</SN>
	<DeviceID>34020000002000000001</DeviceID>
	<Status>OK</Status>
</Notify>`

		// Get initial last seen time
		platform, _ := stateManager.GetPlatform("34020000002000000001")
		initialLastSeen := platform.LastSeen

		// Wait a bit to ensure time difference
		time.Sleep(10 * time.Millisecond)

		// Test keepalive handling
		err := server.handleKeepalive("34020000002000000001", []byte(keepaliveXML))
		if err != nil {
			t.Errorf("Keepalive handling failed: %v", err)
		}

		// Verify last seen time was updated
		platform, _ = stateManager.GetPlatform("34020000002000000001")
		if !platform.LastSeen.After(initialLastSeen) {
			t.Error("Platform last seen time was not updated")
		}
	})

	t.Run("HandleDeviceInfo", func(t *testing.T) {
		deviceInfoXML := `<?xml version="1.0" encoding="UTF-8"?>
<Response>
	<CmdType>DeviceInfo</CmdType>
	<SN>789</SN>
	<DeviceID>34020000002000000001</DeviceID>
	<DeviceName>Hikvision Platform</DeviceName>
	<Manufacturer>Hikvision</Manufacturer>
	<Model>DS-7816N-K2</Model>
	<Firmware>V4.30.000</Firmware>
	<Channel>16</Channel>
</Response>`

		// Test device info handling (should not error)
		server.handleDeviceInfo("34020000002000000001", []byte(deviceInfoXML))

		// Verify platform still exists
		_, exists := stateManager.GetPlatform("34020000002000000001")
		if !exists {
			t.Error("Platform should still exist after device info handling")
		}
	})
}
